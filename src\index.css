/* Imported Fonts */
@import url("https://cdn.jsdelivr.net/gh/sun-typeface/SUITE@2/fonts/static/woff2/SUITE.css");
@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap");

/* Tailwind CSS */
@layer theme, base, components, utilities;
@import "tailwindcss/theme.css" layer(theme);
/* 모든 스타일링을 리셋하는 preflight 비활성화 */
/* @import "tailwindcss/preflight.css" layer(base); */
@import "tailwindcss/utilities.css" layer(utilities);

/* 커스텀 폰트 정의 */
/* 한글을 별도의 입력 없이 자동으로 Pretendard 적용*/
@font-face {
  font-family: "Pretendard-kor";
  src: url("https://fastly.jsdelivr.net/gh/Project-Noonnu/noonfonts_2107@1.1/Pretendard-Regular.woff")
    format("woff");
  unicode-range: U+AC00-D7A3;
}

/* 기본 폰트 적용 및 문제 발생 시 sans-serif로 대체 */
body {
  font-family: "Pretendard-kor", "Montserrat", sans-serif;
}

/* 한글 타이틀 전용 폰트 */
.font-korean-title {
  font-family: "SUITE", sans-serif;
}

@theme {
  --color-primary: #ff7710;
}

@media (prefers-color-scheme: dark) {
}
